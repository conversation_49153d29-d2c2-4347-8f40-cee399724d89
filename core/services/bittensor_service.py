"""
Shared Bittensor service for Validator Token Gateway.

This service handles all Bittensor network interactions and is used by both
local development and production environments.
"""

import hashlib
import time
from typing import Optional, <PERSON><PERSON>

import bittensor

from core.config import settings


class BittensorService:
    """Service for interacting with the Bittensor network."""

    # Class-level cache for components to reduce memory usage
    _cached_subtensor: Optional[bittensor.subtensor] = None
    _cached_metagraph: Optional[bittensor.metagraph] = None
    _cache_timestamp: float = 0
    _cache_ttl: int = 300  # 5 minutes cache TTL

    @classmethod
    def get_components(cls) -> Tuple[bittensor.subtensor, bittensor.metagraph]:
        """Get Bittensor subtensor and metagraph components with caching."""
        current_time = time.time()

        # Check if cache is valid
        if (cls._cached_subtensor is not None and
            cls._cached_metagraph is not None and
            current_time - cls._cache_timestamp < cls._cache_ttl):
            return cls._cached_subtensor, cls._cached_metagraph

        # Create new components if cache is invalid or empty
        cls._cached_subtensor = bittensor.subtensor(network=settings.active_network)
        cls._cached_metagraph = bittensor.metagraph(
            netuid=settings.active_netuid, subtensor=cls._cached_subtensor
        )
        # Sync the metagraph to get the latest state
        cls._cached_metagraph.sync()
        cls._cache_timestamp = current_time

        return cls._cached_subtensor, cls._cached_metagraph

    @classmethod
    def clear_cache(cls):
        """Clear the cached components to free memory."""
        cls._cached_subtensor = None
        cls._cached_metagraph = None
        cls._cache_timestamp = 0

    @staticmethod
    def verify_signature(signature: str, message: str, ss58_address: str) -> bool:
        """Verify a signature against a message and SS58 address."""
        try:
            # Create a keypair from the SS58 address
            keypair = bittensor.Keypair(ss58_address=ss58_address)

            # Convert hex signature back to bytes
            signature_bytes = bytes.fromhex(signature)

            # Try method 1: Verify against message directly (most common)
            message_bytes = message.encode()
            result = keypair.verify(message_bytes, signature_bytes)

            if result:
                return True

            # Try method 2: Verify against hash of message (fallback)
            data_hash = hashlib.sha256(message.encode()).digest()
            result = keypair.verify(data_hash, signature_bytes)

            return result
        except Exception as e:
            # Keep important error logging for troubleshooting
            print(f"Signature verification error: {str(e)}")
            return False

    @staticmethod
    def is_validator(metagraph, ss58_address: str) -> tuple[bool, int, float, float]:
        """
        Check if a hotkey is a validator in the metagraph.

        Returns:
            tuple: (is_validator, uid, stake, trust)
        """
        # Check if the hotkey is in the metagraph
        if ss58_address not in metagraph.hotkeys:
            return False, -1, 0.0, 0.0

        # Get the UID for the hotkey
        uid = metagraph.hotkeys.index(ss58_address)

        # Get stake and trust
        stake = float(metagraph.S[uid])
        trust = float(metagraph.T[uid])

        # Validators have stake (S), miner usually doesn't
        # This is SN27 side of setting for validator permit stake
        is_validator = stake > 1.0e4

        return is_validator, uid, stake, trust
