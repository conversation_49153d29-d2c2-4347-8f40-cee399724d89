"""
Validator Token Gateway - Cloud Function (PRODUCTION).

This is the production serverless implementation of the Validator Token Gateway.
It provides authentication and token generation services for Bittensor validators
using Google Cloud Functions Gen2.

Endpoints:
- POST /auth/token - JWT token generation with Bittensor signature auth
- POST /auth/pubsub-token - OAuth2 token for Google Pub/Sub access
- GET /cleanup - Automated cleanup of expired tokens (Cloud Scheduler only)

Features:
- Serverless auto-scaling architecture
- Cloud SQL for data persistence and rate limiting
- No Redis dependency (replaced with SQL-based rate limiting)
- Native Cloud Functions HTTP handling (no Flask)
- Automated daily cleanup via Cloud Scheduler
"""

import json
import time

import functions_framework
from sqlalchemy import create_engine
from sqlalchemy.orm import Session, sessionmaker

from core.config import settings
from core.services import AuthService, GoogleService, RateLimitService, TokenService
from core.services.bittensor_service import BittensorService

# Initialize database connection with optimized settings
engine = create_engine(
    settings.DATABASE_URL,
    pool_size=5,  # Limit connection pool size
    max_overflow=10,  # Maximum overflow connections
    pool_pre_ping=True,  # Validate connections before use
    pool_recycle=3600,  # Recycle connections every hour
)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


def get_db() -> Session:
    """Get database session with proper cleanup."""
    return SessionLocal()


def get_client_ip(request) -> str:
    """Extract client IP from request."""
    # Check for forwarded headers first (common in cloud environments)
    forwarded_for = request.headers.get("X-Forwarded-For")
    if forwarded_for:
        return forwarded_for.split(",")[0].strip()

    real_ip = request.headers.get("X-Real-IP")
    if real_ip:
        return real_ip

    return request.environ.get("REMOTE_ADDR", "unknown")


def handle_auth_token(request, db: Session) -> dict:
    """
    Handle /auth/token endpoint - JWT token generation with Bittensor signature auth.
    """
    try:
        # Rate limiting
        client_ip = get_client_ip(request)
        if not RateLimitService.check_rate_limit(db, client_ip, settings.RATE_LIMIT):
            return {"error": "Rate limit exceeded", "status_code": 429}

        # Authenticate validator using Bittensor signature
        validator = AuthService.authenticate_validator(request, db)
        if not validator:
            return {"error": "Authentication failed", "status_code": 401}

        # Create JWT token
        token = TokenService.create_token(validator, db)
        expiry_time = time.time() + settings.TOKEN_EXPIRY

        return {
            "token": token.access_token,  # For validator compatibility
            "access_token": token.access_token,  # For backward compatibility
            "token_type": token.token_type,
            "expires_at": expiry_time,
            "expires_in": settings.TOKEN_EXPIRY,
            "status_code": 200,
        }

    except ValueError as e:
        # Keep important error logging for troubleshooting
        print(f"Auth token validation error: {str(e)}")
        return {"error": str(e), "status_code": 400}
    except Exception as e:
        # Keep important error logging for troubleshooting
        print(f"Auth token error: {str(e)}")
        return {"error": f"Internal server error: {str(e)}", "status_code": 500}


def handle_pubsub_token(request, db: Session) -> dict:
    """
    Handle /auth/pubsub-token endpoint - OAuth2 token for Pub/Sub access.
    """
    try:
        # Rate limiting
        client_ip = get_client_ip(request)
        if not RateLimitService.check_rate_limit(db, client_ip, settings.RATE_LIMIT):
            return {"error": "Rate limit exceeded", "status_code": 429}

        # Authenticate validator using JWT token
        validator = AuthService.authenticate_validator_from_token(request, db)
        if not validator:
            return {"error": "Authentication failed", "status_code": 401}

        # Generate Pub/Sub token
        token, expiry = GoogleService.generate_pubsub_token(validator.hotkey, db)

        return {
            "access_token": token,
            "expires_in": expiry - int(time.time()),
            "token_type": "Bearer",
            "scope": " ".join(GoogleService.PUBSUB_SCOPE),
            "topic_paths": GoogleService.get_pubsub_topic_paths(),
            "status_code": 200,
        }

    except ValueError as e:
        # Keep important error logging for troubleshooting
        print(f"Pub/Sub token validation error: {str(e)}")
        return {"error": str(e), "status_code": 400}
    except RuntimeError as e:
        # Keep important error logging for troubleshooting
        print(f"Pub/Sub token runtime error: {str(e)}")
        return {"error": str(e), "status_code": 500}
    except Exception as e:
        # Keep important error logging for troubleshooting
        print(f"Pub/Sub token error: {str(e)}")
        return {"error": f"Internal server error: {str(e)}", "status_code": 500}


def handle_miner_auth_token(request, db: Session) -> dict:
    """
    Handle /auth/miner-token endpoint - JWT token generation with Bittensor auth.
    """
    try:
        # Rate limiting
        client_ip = get_client_ip(request)
        if not RateLimitService.check_rate_limit(db, client_ip, settings.RATE_LIMIT):
            return {"error": "Rate limit exceeded", "status_code": 429}

        # Authenticate miner using Bittensor signature
        miner = AuthService.authenticate_miner(request, db)
        if not miner:
            return {"error": "Authentication failed", "status_code": 401}

        # Create JWT token
        token = TokenService.create_token(miner, db)
        expiry_time = time.time() + settings.TOKEN_EXPIRY

        return {
            "token": token.access_token,  # For validator compatibility
            "access_token": token.access_token,  # For backward compatibility
            "token_type": token.token_type,
            "expires_at": expiry_time,
            "expires_in": settings.TOKEN_EXPIRY,
            "status_code": 200,
        }

    except ValueError as e:
        # Keep important error logging for troubleshooting
        print(f"Auth token validation error: {str(e)}")
        return {"error": str(e), "status_code": 400}
    except Exception as e:
        # Keep important error logging for troubleshooting
        print(f"Auth token error: {str(e)}")
        return {"error": f"Internal server error: {str(e)}", "status_code": 500}


def handle_miner_pubsub_token(request, db: Session) -> dict:
    """
    Handle /auth/miner-pubsub-token endpoint - OAuth2 token for Pub/Sub access.
    """
    try:
        # Rate limiting
        client_ip = get_client_ip(request)
        if not RateLimitService.check_rate_limit(db, client_ip, settings.RATE_LIMIT):
            return {"error": "Rate limit exceeded", "status_code": 429}

        # Authenticate miner using JWT token
        miner = AuthService.authenticate_miner_from_token(request, db)
        if not miner:
            return {"error": "Authentication failed", "status_code": 401}

        # Generate Pub/Sub token
        token, expiry = GoogleService.generate_pubsub_token(miner.hotkey, db)

        return {
            "access_token": token,
            "expires_in": expiry - int(time.time()),
            "token_type": "Bearer",
            "scope": " ".join(GoogleService.PUBSUB_SCOPE),
            "topic_paths": GoogleService.get_pubsub_topic_paths(),
            "status_code": 200,
        }

    except ValueError as e:
        # Keep important error logging for troubleshooting
        print(f"Pub/Sub token validation error: {str(e)}")
        return {"error": str(e), "status_code": 400}
    except RuntimeError as e:
        # Keep important error logging for troubleshooting
        print(f"Pub/Sub token runtime error: {str(e)}")
        return {"error": str(e), "status_code": 500}
    except Exception as e:
        # Keep important error logging for troubleshooting
        print(f"Pub/Sub token error: {str(e)}")
        return {"error": f"Internal server error: {str(e)}", "status_code": 500}


def handle_cleanup(request, db: Session) -> dict:
    """
    Handle /cleanup endpoint - Clean up expired tokens and old records.
    This endpoint is intended to be called by Cloud Scheduler.

    Note: Cleanup operates across ALL environments (production + development).
    """
    try:
        # Verify this is an internal request (from Cloud Scheduler)
        # Cloud Scheduler adds this header when using OIDC authentication
        auth_header = request.headers.get("Authorization", "")
        if not auth_header.startswith("Bearer "):
            return {
                "error": "Unauthorized - missing or invalid authorization",
                "status_code": 401
            }

        cleanup_results = {}

        # Clean up expired Pub/Sub tokens
        pubsub_cleaned = GoogleService.cleanup_expired_pubsub_tokens(db)
        cleanup_results["pubsub_tokens_cleaned"] = pubsub_cleaned

        # Clean up expired JWT tokens
        jwt_cleaned = TokenService.cleanup_expired_tokens(db)
        cleanup_results["jwt_tokens_cleaned"] = jwt_cleaned

        # Clean up old rate limit records (older than 24 hours)
        rate_limits_cleaned = RateLimitService.cleanup_old_rate_limits(db, hours_old=24)
        cleanup_results["rate_limits_cleaned"] = rate_limits_cleaned

        # Clear Bittensor cache to free memory
        BittensorService.clear_cache()
        cleanup_results["bittensor_cache_cleared"] = True

        cleanup_results["status"] = "success"
        cleanup_results["timestamp"] = time.time()
        cleanup_results["environment"] = settings.ENVIRONMENT
        cleanup_results["status_code"] = 200

        return cleanup_results

    except Exception as e:
        return {
            "error": f"Cleanup failed: {str(e)}",
            "status": "error",
            "timestamp": time.time(),
            "environment": settings.ENVIRONMENT,
            "status_code": 500
        }


@functions_framework.http
def auth_handler(request):
    """
    Main Cloud Function entry point.
    Routes requests to appropriate handlers based on path and method.
    """
    db = None
    try:
        # Validate request method
        if request.method not in ["POST", "GET"]:
            return (
                json.dumps({"error": "Method not allowed"}),
                405,
                {"Content-Type": "application/json"},
            )

        # Get database session
        db = get_db()

        # Route based on path
        path = request.path.rstrip("/")

        # Authentication endpoints (POST only)
        if path in ["/auth/token", "/token"] and request.method == "POST":
            result = handle_auth_token(request, db)
        elif (path in ["/auth/pubsub-token", "/pubsub-token"] and
              request.method == "POST"):
            result = handle_pubsub_token(request, db)
        elif path in ["/auth/miner-token", "/miner-token"] and request.method == "POST":
            result = handle_miner_auth_token(request, db)
        elif (path in ["/auth/miner-pubsub-token", "/miner-pubsub-token"] and
              request.method == "POST"):
            result = handle_miner_pubsub_token(request, db)
        elif path in ["/cleanup", "/auth/cleanup"] and request.method == "GET":
            result = handle_cleanup(request, db)
        else:
            return (
                json.dumps({"error": "Endpoint not found"}),
                404,
                {"Content-Type": "application/json"},
            )

        # Extract status code and return response
        status_code = result.pop("status_code", 200)
        return (json.dumps(result), status_code, {"Content-Type": "application/json"})

    except Exception as e:
        return (
            json.dumps({"error": f"Unexpected error: {str(e)}"}),
            500,
            {"Content-Type": "application/json"},
        )

    finally:
        if db:
            db.close()
