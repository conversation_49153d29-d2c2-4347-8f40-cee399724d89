[project]
name = "validator-token-gateway"
version = "2.0.0"
description = "A Cloud Function gateway for authenticating validators and issuing secure tokens"
authors = [
    {name = "Your Name", email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.12,<3.13"
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.12",
]
dependencies = [
    # Core dependencies for Cloud Function
    "pydantic>=2.11.5",
    "pydantic-settings>=2.2.1",
    "sqlalchemy>=2.0.41",
    "psycopg2-binary>=2.9.10",
    "pyjwt>=2.10.1",
    "bittensor>=9.10.1",
    # Google Cloud dependencies
    "google-auth>=2.28.2",
    "google-cloud-pubsub>=2.22.0",
    "google-cloud-core>=2.4.1",
    "google-cloud-secret-manager>=2.16.1",
]

[project.optional-dependencies]
dev = [
    "ruff>=0.11.11",
    "pre-commit>=4.2.0",
    "pytest>=7.4.0",
    "pytest-cov>=4.1.0",
    "pytest-asyncio==0.21.2",
    "redis>=5.0.0",  # For GCP Secret Manager emulation in tests
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["core"]

[tool.ruff]
line-length = 88
indent-width = 4
target-version = "py312"

[tool.ruff.lint]
# Enable flake8-bugbear (`B`) rules.
select = ["E", "F", "B", "I"]
ignore = []

[tool.ruff.format]
# Use double quotes for strings.
quote-style = "double"

# Indent with spaces, rather than tabs.
indent-style = "space"

# Respect magic trailing commas.
skip-magic-trailing-comma = false

# Automatically detect the appropriate line ending.
line-ending = "auto"

[dependency-groups]
dev = [
    "httpx>=0.28.1",
    "pre-commit>=4.2.0",
    "pytest>=8.3.5",
    "pytest-asyncio>=0.21.2",
    "pytest-cov>=6.1.1",
    "redis>=6.2.0",
    "ruff>=0.11.11",
]
