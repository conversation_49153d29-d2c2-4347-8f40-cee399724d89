#!/bin/bash

# Memory Optimization Script for Cloud Functions
# This script optimizes the function package for minimal memory usage

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${GREEN}🚀 Starting memory optimization...${NC}"

# Function to clean Python cache files
clean_python_cache() {
    echo -e "${YELLOW}🧹 Cleaning Python cache files...${NC}"
    find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
    find . -type f -name "*.pyc" -delete 2>/dev/null || true
    find . -type f -name "*.pyo" -delete 2>/dev/null || true
    find . -type d -name ".pytest_cache" -exec rm -rf {} + 2>/dev/null || true
    echo -e "${GREEN}✅ Python cache cleaned${NC}"
}

# Function to optimize package size
optimize_package() {
    echo -e "${YELLOW}📦 Optimizing package size...${NC}"
    
    # Remove unnecessary files from the package
    if [ -d "/tmp/function-package" ]; then
        cd /tmp/function-package
        
        # Remove test files
        find . -name "*test*.py" -delete 2>/dev/null || true
        find . -name "test_*" -delete 2>/dev/null || true
        find . -name "*_test.py" -delete 2>/dev/null || true
        
        # Remove documentation files
        find . -name "*.md" -delete 2>/dev/null || true
        find . -name "*.rst" -delete 2>/dev/null || true
        find . -name "*.txt" -delete 2>/dev/null || true
        
        # Remove development files
        find . -name ".git*" -delete 2>/dev/null || true
        find . -name "*.log" -delete 2>/dev/null || true
        find . -name "*.tmp" -delete 2>/dev/null || true
        
        # Clean Python cache again
        find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
        find . -type f -name "*.pyc" -delete 2>/dev/null || true
        
        cd - > /dev/null
    fi
    
    echo -e "${GREEN}✅ Package optimized${NC}"
}

# Function to check package size
check_package_size() {
    echo -e "${YELLOW}📏 Checking package size...${NC}"
    
    if [ -d "/tmp/function-package" ]; then
        size=$(du -sh /tmp/function-package | cut -f1)
        echo -e "${BLUE}📦 Package size: ${size}${NC}"
        
        # Warn if package is too large
        size_bytes=$(du -s /tmp/function-package | cut -f1)
        if [ "$size_bytes" -gt 512000 ]; then  # 500MB in KB
            echo -e "${YELLOW}⚠️  Warning: Package size is large (${size}). Consider removing unnecessary dependencies.${NC}"
        else
            echo -e "${GREEN}✅ Package size is optimal${NC}"
        fi
    fi
}

# Function to optimize requirements
optimize_requirements() {
    echo -e "${YELLOW}📋 Optimizing requirements...${NC}"
    
    if [ -f "functions/auth_function/requirements.txt" ]; then
        # Create optimized requirements file
        cat > functions/auth_function/requirements.txt << EOF
# Cloud Function dependencies - optimized for memory
functions-framework==3.*

# Database - minimal versions
sqlalchemy==2.*
psycopg2-binary==2.*

# Authentication and JWT
PyJWT==2.*
bittensor>=7.0.0

# Google Cloud - minimal versions
google-cloud-secret-manager==2.*
google-auth==2.*
google-cloud-pubsub==2.*

# Configuration - minimal versions
pydantic==2.*
pydantic-settings==2.*
EOF
        echo -e "${GREEN}✅ Requirements optimized${NC}"
    fi
}

# Function to show memory optimization tips
show_memory_tips() {
    echo -e "${BLUE}💡 Memory Optimization Tips:${NC}"
    echo -e "${YELLOW}1. Bittensor components are cached for 5 minutes to reduce memory usage${NC}"
    echo -e "${YELLOW}2. Database connections use connection pooling with limits${NC}"
    echo -e "${YELLOW}3. Sessions are properly closed to prevent memory leaks${NC}"
    echo -e "${YELLOW}4. Cleanup endpoint clears Bittensor cache${NC}"
    echo -e "${YELLOW}5. Package size is optimized by removing unnecessary files${NC}"
    echo ""
    echo -e "${BLUE}🔧 Manual optimizations you can make:${NC}"
    echo -e "${YELLOW}• Monitor memory usage with Cloud Functions logs${NC}"
    echo -e "${YELLOW}• Consider increasing cache TTL if validator data doesn't change often${NC}"
    echo -e "${YELLOW}• Use the cleanup endpoint regularly to free memory${NC}"
    echo -e "${YELLOW}• Monitor database connection pool usage${NC}"
}

# Main execution
main() {
    echo -e "${GREEN}🎯 Memory Optimization for Cloud Functions${NC}"
    echo ""
    
    # Clean cache files
    clean_python_cache
    
    # Optimize requirements
    optimize_requirements
    
    # If function package exists, optimize it
    if [ -d "/tmp/function-package" ]; then
        optimize_package
        check_package_size
    else
        echo -e "${YELLOW}ℹ️  Function package not found. Run prepare_function_source.sh first.${NC}"
    fi
    
    # Show tips
    show_memory_tips
    
    echo ""
    echo -e "${GREEN}✅ Memory optimization complete!${NC}"
    echo -e "${BLUE}📊 Current memory allocation: 4096M (4GB)${NC}"
    echo -e "${BLUE}📈 Available memory: ~3900M (after system overhead)${NC}"
}

# Run main function
main "$@"
