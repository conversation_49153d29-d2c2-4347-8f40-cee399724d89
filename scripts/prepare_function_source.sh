#!/bin/bash
set -e

# This script prepares the source code for the Cloud Function by packaging
# the function-specific code with the shared 'core' module.
# Only essential files are copied to minimize deployment package size.

SCRIPT_DIR=$(dirname "$0")
PROJECT_ROOT=$(realpath "$SCRIPT_DIR/..")
SOURCE_DIR="/tmp/function-package"

echo "Preparing function source in $SOURCE_DIR..."

# Clean up previous package
rm -rf "$SOURCE_DIR"

# Create package directory
mkdir -p "$SOURCE_DIR"

# Copy only essential function files
echo "Copying function files..."
cp "$PROJECT_ROOT/functions/auth_function/main.py" "$SOURCE_DIR/"
cp "$PROJECT_ROOT/functions/auth_function/requirements.txt" "$SOURCE_DIR/"

# Copy services directory if it exists
if [ -d "$PROJECT_ROOT/functions/auth_function/services" ]; then
    cp -r "$PROJECT_ROOT/functions/auth_function/services" "$SOURCE_DIR/"
fi

echo "Copying core module..."
cp -r "$PROJECT_ROOT/core" "$SOURCE_DIR/"

echo "✅ Function source prepared successfully."
